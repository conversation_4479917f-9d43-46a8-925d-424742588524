using Arabdt.Talmatha.Api.Base;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Arabdt.Talmatha.Application.CheckExpirationFromTokenMiddleware;
using System.Security.Claims;

namespace Arabdt.Talmatha.Api.Controllers.Website
{
    [ApiController]
    [Route(Routes.API)]
    public class DiagnosticController : BaseController
    {
        public DiagnosticController(IMediator mediator, ICurrentUserService currentUser)
            : base(mediator, currentUser)
        { }

        [HttpGet("test-no-auth")]
        public IActionResult TestNoAuth()
        {
            return Ok(new { 
                message = "Endpoint accessible without authentication",
                timestamp = DateTime.UtcNow,
                controller = "Diagnostic",
                action = "TestNoAuth"
            });
        }

        [HttpGet("test-with-auth")]
        [Authorize]
        public IActionResult TestWithAuth()
        {
            var claims = User.Claims.Select(c => new { c.Type, c.Value }).ToList();
            var userId = _currentUser?.UserId;
            
            return Ok(new { 
                message = "Endpoint accessible with authentication",
                timestamp = DateTime.UtcNow,
                controller = "Diagnostic",
                action = "TestWithAuth",
                userId = userId,
                userClaims = claims,
                isAuthenticated = User.Identity?.IsAuthenticated ?? false,
                authType = User.Identity?.AuthenticationType
            });
        }

        [HttpGet("test-role-trainee")]
        [Authorize(Roles = "Trainee")]
        public IActionResult TestRoleTrainee()
        {
            var claims = User.Claims.Select(c => new { c.Type, c.Value }).ToList();
            var userId = _currentUser?.UserId;
            
            return Ok(new { 
                message = "Endpoint accessible with Trainee role",
                timestamp = DateTime.UtcNow,
                controller = "Diagnostic",
                action = "TestRoleTrainee",
                userId = userId,
                userClaims = claims,
                roles = User.FindAll(ClaimTypes.Role).Select(c => c.Value).ToList()
            });
        }

        [HttpGet("test-company-roles")]
        [Authorize(Roles = "CompanyRepresentative,CompanyDelegate")]
        public IActionResult TestCompanyRoles()
        {
            var claims = User.Claims.Select(c => new { c.Type, c.Value }).ToList();
            var userId = _currentUser?.UserId;
            
            return Ok(new { 
                message = "Endpoint accessible with Company roles",
                timestamp = DateTime.UtcNow,
                controller = "Diagnostic",
                action = "TestCompanyRoles",
                userId = userId,
                userClaims = claims,
                roles = User.FindAll(ClaimTypes.Role).Select(c => c.Value).ToList()
            });
        }
    }
}
