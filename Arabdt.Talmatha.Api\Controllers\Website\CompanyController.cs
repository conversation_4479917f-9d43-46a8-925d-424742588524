using Arabdt.Talmatha.Api.Base;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Arabdt.Talmatha.Application.Features.Company.GetCompanyDetailsById;
using Arabdt.Talmatha.Application.Features.Company.EditCompanyMainData;
using Arabdt.Talmatha.Application.Features.Company.RemoveCompanyDelegate;
using Arabdt.Talmatha.Application.Features.Company.AddCompanyDelegate;
using Arabdt.Talmatha.Application.Features.Company.ReplaceCompanyRepresentitive;
using Arabdt.Talmatha.Application.CheckExpirationFromTokenMiddleware;
using Microsoft.AspNetCore.Authorization;

namespace Arabdt.Talmatha.Api.Controllers.Website
{
    [ApiController]
    [Route(Routes.API)]
    public class CompanyController : BaseController
    {
        public CompanyController(IMediator mediator, ICurrentUserService currentUser)
            : base(mediator, currentUser)
        { }


        [HttpGet("get-company-details-by-id")]
        [Authorize]
        public async Task<IActionResult> GetById()
        {
            return Ok(await Mediator.Send(new GetCompanyDetailsByIdQuery() { UserId = _currentUser.UserId ?? string.Empty }));
        }
        [HttpPut("edit-company-main-data")]
        [Authorize(Roles = "CompanyRepresentative,CompanyDelegate")]
        public async Task<IActionResult> EditMainData([FromBody] EditCompanyMainDataCommand editCompanyMainDataCommand)
        {
            return Ok(await Mediator.Send(editCompanyMainDataCommand));
        }
        [HttpPut("remove-delegate")]
        //[Authorize(Roles = "CompanyRepresentative,CompanyDelegate")]
        public async Task<IActionResult> RemoveDelegate([FromBody] RemoveCompanyDelegateCommand removeCompanyDelegateCommand)
        {
            return Ok(await Mediator.Send(removeCompanyDelegateCommand));
        }
        [HttpPut("add-delegate")]
        //[Authorize(Roles = "CompanyRepresentative,CompanyDelegate")]
        public async Task<IActionResult> AddDelegate([FromBody] AddCompanyDelegateCommand addCompanyDelegateCommand)
        {
            return Ok(await Mediator.Send(addCompanyDelegateCommand));
        }
        [HttpPut("replace-main-representitive")]
        //[Authorize(Roles = "CompanyRepresentative")]
        public async Task<IActionResult> ReplaceCompanyRepresentitive([FromBody] ReplaceCompanyRepresentitiveCommand replaceCompanyRepresentitiveCommand)
        {
            return Ok(await Mediator.Send(replaceCompanyRepresentitiveCommand));
        }
        [HttpGet("get-company-active-delegates")]
        //[Authorize(Roles = "CompanyRepresentative,CompanyDelegate")]
        public async Task<IActionResult> GetCompanyActiveDelegates(int companyId)
        {
            return Ok(await Mediator.Send(new GetCompanyDetailsByIdQuery()));
        }
    }
}
