using System.Globalization;
using System.Security.Claims;
using System.Text;
using Arabdt.Talmatha.Api.Base.Behaviors;
using Arabdt.Talmatha.Api.Middleware;
using Arabdt.Talmatha.Application.CheckExpirationFromTokenMiddleware;
using Arabdt.Talmatha.Application.Features.Company.AddCompany;
using Arabdt.Talmatha.Application.Integrations.Moc;
using Arabdt.Talmatha.Application.Integrations.Nafath;
using Arabdt.Talmatha.Application.Profiles;
using Arabdt.Talmatha.Application.Services.ForgetRestPasswordService;
using Arabdt.Talmatha.Application.Services.LoginService;
using Arabdt.Talmatha.Application.SignalRService;
using Arabdt.Talmatha.Domain.Entities.Identity;
using Arabdt.Talmatha.Domain.Interfaces;
using Arabdt.Talmatha.Infrastructure.Context;
using Arabdt.Talmatha.Infrastructure.Repositories;
using Arabdt.Talmatha.SharedKernel.Helpers.EmailHelper;
using Arabdt.Talmatha.SharedKernel.Helpers.FileHelper;
using Arabdt.Talmatha.SharedKernel.Helpers.OTPHelper;
using Arabdt.Talmatha.SharedKernel.Helpers.SMSHelper;
using Arabdt.Talmatha.SharedKernel.Logger;
using Arabdt.Talmatha.SharedKernel.ModelHelpers;
using FluentValidation;
using MediatR;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Localization;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using NLog;

var builder = WebApplication.CreateBuilder(args);

#region App Setting
AppSetting appSettings = new AppSetting();
builder.Configuration.Bind(appSettings);
builder.Services.AddSingleton(appSettings);
#endregion

#region Logger
LogManager.Setup().LoadConfigurationFromFile(string.Concat(Directory.GetCurrentDirectory(), "/nlog.config"));
builder.Services.AddSingleton<ILoggerManager, LoggerManager>();
#endregion

#region Localization
builder.Services.AddLocalization(opt =>
{
    opt.ResourcesPath = "";
});

builder.Services.Configure<RequestLocalizationOptions>(opt =>
{
    List<CultureInfo> locales = new List<CultureInfo> { new CultureInfo("en-US"), new CultureInfo("ar-SA"), };
    opt.DefaultRequestCulture = new RequestCulture("ar-SA");
    opt.SupportedCultures = locales;
    opt.SupportedUICultures = locales;
});
#endregion

#region CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowedOrigins",
        builder =>
        {
            builder.WithOrigins(appSettings.AllowedOrigins.ToArray())
                   .AllowAnyMethod()
                   .AllowAnyHeader()
                   .AllowCredentials();
        });
});
#endregion
#region JWT
builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuer = true,
        ValidateAudience = true,
        ValidateLifetime = true,
        ValidateIssuerSigningKey = true,
        ValidIssuer = appSettings.Jwt.Issuer,
        ValidAudience = appSettings.Jwt.Audience,
        IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(appSettings.Jwt.Key)),
        RoleClaimType = ClaimTypes.Role,
        NameClaimType = ClaimTypes.Name
    };
});

#endregion
#region Swagger
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo
    {
        Title = "Arabdt.Talmatha.Api",
        Version = "v1"
    });

    // 🔹 Correct security definition
    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Name = "Authorization",
        Type = SecuritySchemeType.Http,       // <-- Use Http, not ApiKey
        Scheme = "Bearer",                    // <-- Must be lowercase "bearer" in actual header
        BearerFormat = "JWT",                 // <-- Optional but clarifies format
        In = ParameterLocation.Header,
        Description = "Enter: Bearer {your JWT token}"
    });

    // 🔹 Security requirement
    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            Array.Empty<string>()
        }
    });
});

#endregion

#region Mapper
builder.Services.AddAutoMapper(typeof(AppMappingProfile).Assembly);
#endregion

#region DB context
//// for read 
//builder.Services.AddDbContext<TalmathaReadContext>(options =>
//{
//    options.UseSqlServer(appSettings.ConnectionStrings.TalmathaReadDb,
//        sqlServerOptionsAction: sqlOptions =>
//        {
//            sqlOptions.MigrationsAssembly("Arabdt.Talmatha.Infrastructure");
//            sqlOptions.EnableRetryOnFailure(maxRetryCount: 15, maxRetryDelay: TimeSpan.FromSeconds(30), errorNumbersToAdd: null);
//        });
//});
// for write
builder.Services.AddDbContext<TalmathaWriteContext>(options =>
{
    options.UseSqlServer(appSettings.ConnectionStrings.TalmathaWriteDb,
        sqlServerOptionsAction: sqlOptions =>
        {
            sqlOptions.MigrationsAssembly("Arabdt.Talmatha.Infrastructure");
            sqlOptions.EnableRetryOnFailure(maxRetryCount: 15, maxRetryDelay: TimeSpan.FromSeconds(30), errorNumbersToAdd: null);
        });
});


builder.Services.Configure<DataProtectionTokenProviderOptions>(opt => opt.TokenLifespan = TimeSpan.FromHours(2));
builder.Services.AddIdentity<ApplicationUser, ApplicationRole>(options =>
{
    // Match BeAStrongPassword rules
    options.Password.RequiredLength = 8;
    options.Password.RequireUppercase = true;
    options.Password.RequireLowercase = true;
    options.Password.RequireDigit = true;
    options.Password.RequireNonAlphanumeric = true;
})
.AddEntityFrameworkStores<TalmathaWriteContext>()
.AddDefaultTokenProviders();


//builder.Services.AddIdentity<ApplicationUser, ApplicationRole>()
//    .AddEntityFrameworkStores<TalmathaReadContext>()
//    .AddDefaultTokenProviders();
#endregion

#region DI
System.Type[] readRepositories = System.Reflection.Assembly.Load(typeof(CompanyReadRepo).Assembly.GetName()).GetTypes().ToArray();
System.Type[] iReadRepositories = System.Reflection.Assembly.Load(typeof(ICompanyReadRepo).Assembly.GetName()).GetTypes().Where(r => r.IsInterface).ToArray();
foreach (var repoInterface in iReadRepositories)
{
    System.Type classType = readRepositories.FirstOrDefault(r => repoInterface.IsAssignableFrom(r));
    if (classType != null)
    {
        // Register implementation with the default DI container
        builder.Services.AddScoped(repoInterface, classType); // or AddTransient, AddSingleton based on your needs

    }
}
System.Type[] writeRepositories = System.Reflection.Assembly.Load(typeof(CompanyWriteRepo).Assembly.GetName()).GetTypes().ToArray();
System.Type[] iWriteRepositories = System.Reflection.Assembly.Load(typeof(ICompanyWriteRepo).Assembly.GetName()).GetTypes().Where(r => r.IsInterface).ToArray();
foreach (var repoInterface in iWriteRepositories)
{
    System.Type classType = writeRepositories.FirstOrDefault(r => repoInterface.IsAssignableFrom(r));
    if (classType != null)
    {
        // Register implementation with the default DI container
        builder.Services.AddScoped(repoInterface, classType); // or AddTransient, AddSingleton based on your needs

    }
}
#endregion

#region Services
builder.Services.AddHttpClient<INafathService, NafathService>();
builder.Services.AddScoped<IFileService, FileService>();
builder.Services.AddScoped<IOTPService, OTPService>();
builder.Services.AddScoped<IEmailService, EmailService>();
builder.Services.AddScoped<ILoginService, LoginService>();
builder.Services.AddScoped<IMoCService, MoCService>();
builder.Services.AddScoped<IForgetRestPasswordService, ForgetRestPasswordService>();
builder.Services.AddScoped<ISMSService, SMSService>();
builder.Services.AddScoped<ICurrentUserService, CurrentUserService>();
#endregion

#region Mediator
builder.Services.AddMediatR(configuration =>
{
    configuration.RegisterServicesFromAssembly(typeof(AddCompanyCommandHandler).Assembly);
    configuration.AddOpenBehavior(typeof(ValidatorBehavior<,>));
});
builder.Services.AddValidatorsFromAssembly(typeof(AddCompanyCommandHandler).Assembly);
builder.Services.AddTransient(typeof(IPipelineBehavior<,>), typeof(ValidatorBehavior<,>));
#endregion

#region Middleware
builder.Services.AddScoped<ExceptionHandlerMiddleware>();
#endregion

builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSignalR();
builder.Services.AddEndpointsApiExplorer(); ;

builder.Services.AddHttpContextAccessor();
builder.Services.AddScoped<ICurrentUserService, CurrentUserService>();

var app = builder.Build();
//The HTTP request pipeline.
#region env
if (app.Environment.IsDevelopment() || app.Environment.IsStaging())
{
    app.UseDeveloperExceptionPage();
    app.UseSwagger();
    app.UseSwaggerUI(c => c.SwaggerEndpoint("/swagger/v1/swagger.json", "Arabdt.Talmatha.Api v1"));
}
var options = app.Services.GetService<IOptions<RequestLocalizationOptions>>();
app.UseRequestLocalization(options.Value);
#endregion

//#region Validate Expired Token
//app.UseMiddleware<JwtValidationMiddleware>();
//#endregion

#region app
app.UseRouting();
app.UseCors("AllowedOrigins");
app.UseMiddleware<ExceptionHandlerMiddleware>();
app.UseAuthentication();
app.UseAuthorization();
#endregion
//app.UseHttpsRedirection();
app.MapControllers();
#region SignalR
app.MapHub<AuthHub>("/authhub");
#endregion
app.Run();

