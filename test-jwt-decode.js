// Simple JWT decoder to verify token contents
const token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.mRfVpVsbouzrfLvZF6PvYoIbV51uZUfv5XQAPOyyIOc";

function decodeJWT(token) {
    const parts = token.split('.');
    if (parts.length !== 3) {
        throw new Error('Invalid JWT token');
    }
    
    const payload = parts[1];
    const decoded = JSON.parse(atob(payload.replace(/-/g, '+').replace(/_/g, '/')));
    return decoded;
}

try {
    const decoded = decodeJWT(token);
    console.log("JWT Token Contents:");
    console.log(JSON.stringify(decoded, null, 2));
    
    console.log("\nKey Information:");
    console.log("User ID (sub):", decoded.sub);
    console.log("Role:", decoded["http://schemas.microsoft.com/ws/2008/06/identity/claims/role"]);
    console.log("Email:", decoded.email);
    console.log("National ID:", decoded.nationalId);
    console.log("Issuer:", decoded.iss);
    console.log("Audience:", decoded.aud);
    console.log("Expires:", new Date(decoded.exp * 1000));
    console.log("Is Expired:", new Date(decoded.exp * 1000) < new Date());
    
} catch (error) {
    console.error("Error decoding JWT:", error);
}
